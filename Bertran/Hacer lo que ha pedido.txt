Sacar los datos facebook ads
Modelo de ML para los ads
Sistema para llamar a leads antiguos
Sistema para mandar a los leads al centro con hora disponible
AI conectada al calendario


MCP -- PROFUNDIZAR Y ABORDAR UN PROYECTO
HARRAMIENTAS DS Y DE -- BUSQUEDA Y APLICACIÓN EXHAUSTIVA
LISTADO DE POSIBLES EMPRESAS -- BUSQUEDA (EXHAUSTIVA)

tfg presentación y módulos asquerosos

0xe5C8fFB0A0e863EE773964CF59B76A65B513F58d

NVIDIA
ENERGY
TESLA
PALUNTEER



chrome://settings/content/usbDevices


INICIO DE LA CONVERSACIÓN DEL AGENTE - SEGUIR ESTRICTAMENTE EL GUIÓN SEGÚN EL ORDEN INDICADO. CUADO SE INDIQUE PASAR EL **Paso X. ve sigue directamente en esa parte del guió. NO ENTRES EN BUCLES A MENOS QUE SEA PARA MIRAR LAS HORAS DISPONIBLES.


            -EL AGENTE TIEN QUE DEFINIR [hora1] y [hora2] utilizando {{free_slots}} que tien rangos de fecha LIBRES, el agente SIEMPRE propone citas DENTRO de estos rangos. El agente selecciona la fecha libre más cercana posible, teniendo en cuenta la variable opcional {{horario}} que es la hora que ha pedido el cliente. Si la hora que ha pedido está ocupada devulve otras dos disponibles. EL AGENTE DEVUELVE SIEMPRE DOS POSIBLES FECHAS DISPONIBLES QUE ALMACENA EN las variables `hora1` y `hora2` que se componen del DÍA Y HORA DISPONIBLES. RESTRICCIONES:  CITAS DE 30 MINUTOS, TIENE QUE ESTAR LIBRE TODO EL INTERVALO. EJEMPLO DE USO: free_slots:[{'date': '2025-06-10', 'free_slots': [{'start': '2025-06-10T12:15:00.000Z',
     'end': '2025-06-10T12:30:00.000Z'},
    {'start': '2025-06-10T13:30:00.000Z', 'end': '2025-06-10T14:00:00.000Z'},
    {'start': '2025-06-10T18:00:00.000Z', 'end': '2025-06-10T19:00:00.000Z'}]}. EN ESTE CASO, se pueden definir [hora1]="el 6 de junion a la una y media de la tarde" y `hora2`="el 10 de junio a las seis o seis y media de la tarde"
