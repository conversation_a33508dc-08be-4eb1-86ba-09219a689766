{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1572ce77", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "452a01f1", "metadata": {}, "outputs": [], "source": ["paid_ads = pd.read_csv('/home/<USER>/Documents/Clibel/ML_ads/data/ads_ready_without_blanks.csv', header=0)"]}, {"cell_type": "code", "execution_count": 4, "id": "7b814983", "metadata": {}, "outputs": [], "source": ["def get_unique_values(df, col):\n", "    return list(set(df[col].values.tolist()))"]}, {"cell_type": "code", "execution_count": 7, "id": "0d5f678c", "metadata": {}, "outputs": [], "source": ["paid_ads.drop(columns=['Unnamed: 0', 'original_index'], inplace=True)"]}, {"cell_type": "code", "execution_count": 8, "id": "0a1aeacc", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Strategic_Offer_Architecture.Primary_Treatment_Subject',\n", "       'Strategic_Offer_Architecture.Sub_Treatment_Focus',\n", "       'Strategic_Offer_Architecture.Offer_Structure',\n", "       'Strategic_Offer_Architecture.Ad_Funnel_Stage',\n", "       'Strategic_Offer_Architecture.CTA_Present',\n", "       'Strategic_Offer_Architecture.Ad_Features',\n", "       'Hook_Deconstruction.Hook_Narrative_Angle',\n", "       'Hook_Deconstruction.Hook_Visual_Subject',\n", "       'Hook_Deconstruction.Hook_Emotional_Tone',\n", "       'Hook_Deconstruction.Hook_Text_Type_OCR',\n", "       'Hook_Deconstruction.Hook_Motion_Video',\n", "       'Hook_Deconstruction.Hook_Format_Style',\n", "       'Hook_Deconstruction.Hook_Audio_Elements',\n", "       'Hook_Deconstruction.Hook_Content_Features',\n", "       'Hook_Deconstruction.Hook_Duration_Secs',\n", "       'Production_Visual_Aesthetics.Visual_Setting',\n", "       'Production_Visual_Aesthetics.Lighting_Style',\n", "       'Production_Visual_Aesthetics.Camera_Angle',\n", "       'Production_Visual_Aesthetics.Editing_Pacing',\n", "       'Production_Visual_Aesthetics.Color_Grading_Scheme',\n", "       'Production_Visual_Aesthetics.Focal_Length_Style',\n", "       'Production_Visual_Aesthetics.Sound_Score',\n", "       'Production_Visual_Aesthetics.Video_Duration_Secs', 'RESULTADOS'],\n", "      dtype='object')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["paid_ads.columns"]}, {"cell_type": "code", "execution_count": 9, "id": "ccba1e0e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Strategic_Offer_Architecture.Primary_Treatment_Subject</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus</th>\n", "      <th>Strategic_Offer_Architecture.Offer_Structure</th>\n", "      <th>Strategic_Offer_Architecture.Ad_Funnel_Stage</th>\n", "      <th>Strategic_Offer_Architecture.CTA_Present</th>\n", "      <th>Strategic_Offer_Architecture.Ad_Features</th>\n", "      <th>Hook_Deconstruction.Hook_Narrative_Angle</th>\n", "      <th>Hook_Deconstruction.Hook_Visual_Subject</th>\n", "      <th>Hook_Deconstruction.Hook_Emotional_Tone</th>\n", "      <th>Hook_Deconstruction.Hook_Text_Type_OCR</th>\n", "      <th>...</th>\n", "      <th>Hook_Deconstruction.Hook_Duration_Secs</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting</th>\n", "      <th>Production_Visual_Aesthetics.Lighting_Style</th>\n", "      <th>Production_Visual_Aesthetics.Camera_Angle</th>\n", "      <th>Production_Visual_Aesthetics.Editing_Pacing</th>\n", "      <th>Production_Visual_Aesthetics.Color_Grading_Scheme</th>\n", "      <th>Production_Visual_Aesthetics.Focal_Length_Style</th>\n", "      <th>Production_Visual_Aesthetics.Sound_Score</th>\n", "      <th>Production_Visual_Aesthetics.Video_Duration_Secs</th>\n", "      <th>RESULTADOS</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Facial</td>\n", "      <td>Wrinkle_Reduction</td>\n", "      <td>2 por 1</td>\n", "      <td>Bottom_of_Funnel</td>\n", "      <td>No</td>\n", "      <td>Testimonial|Actual_Treatment_Shown|On-Screen_P...</td>\n", "      <td>Testimonial_Focused</td>\n", "      <td>Extreme_Close_Up_Problem_Area</td>\n", "      <td>Aspirational_Positive</td>\n", "      <td>Benefit_Promise</td>\n", "      <td>...</td>\n", "      <td>5.0</td>\n", "      <td>Clinical_Room</td>\n", "      <td>Bright_High_Key</td>\n", "      <td>Eye_Level</td>\n", "      <td>Fast</td>\n", "      <td>High_Saturation</td>\n", "      <td>Standard_Lens</td>\n", "      <td>Voiceover_With_Music</td>\n", "      <td>24.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Facial</td>\n", "      <td>Wrinkle_Reduction</td>\n", "      <td>2 por 1</td>\n", "      <td>Middle_of_Funnel</td>\n", "      <td>No</td>\n", "      <td>Testimonial|Actual_Treatment_Shown</td>\n", "      <td>Benefit_Focused</td>\n", "      <td>Process_Shot_Treatment_In_Progress</td>\n", "      <td>Aspirational_Positive</td>\n", "      <td>No_Text</td>\n", "      <td>...</td>\n", "      <td>4.0</td>\n", "      <td>Clinical_Room</td>\n", "      <td>Bright_High_Key</td>\n", "      <td>Eye_Level</td>\n", "      <td>Medium</td>\n", "      <td><PERSON>_Tones</td>\n", "      <td>Standard_Lens</td>\n", "      <td>Voiceover_With_Music</td>\n", "      <td>25.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Facial</td>\n", "      <td>Wrinkle_Reduction</td>\n", "      <td>2 por 1</td>\n", "      <td>Bottom_of_Funnel</td>\n", "      <td>Yes</td>\n", "      <td>Testimonial|Actual_Treatment_Shown|On-Screen_P...</td>\n", "      <td>Benefit_Focused</td>\n", "      <td>Extreme_Close_Up_Problem_Area</td>\n", "      <td>Aspirational_Positive</td>\n", "      <td>Offer_Headline</td>\n", "      <td>...</td>\n", "      <td>5.0</td>\n", "      <td>Clinical_Room</td>\n", "      <td>Bright_High_Key</td>\n", "      <td>Eye_Level</td>\n", "      <td>Medium</td>\n", "      <td><PERSON>_Tones</td>\n", "      <td>Standard_Lens</td>\n", "      <td>Voiceover_With_Music</td>\n", "      <td>25.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Facial</td>\n", "      <td>Wrinkle_Reduction</td>\n", "      <td>2 por 1</td>\n", "      <td>Middle_of_Funnel</td>\n", "      <td>No</td>\n", "      <td>Ad_Recording_Spokesperson|Actual_Treatment_Shown</td>\n", "      <td>Problem_Focused</td>\n", "      <td>C<PERSON>_Talking_Head_UGC</td>\n", "      <td>Aspirational_Positive</td>\n", "      <td>Bold_Statement</td>\n", "      <td>...</td>\n", "      <td>5.0</td>\n", "      <td>Clinical_Room</td>\n", "      <td>Bright_High_Key</td>\n", "      <td>Eye_Level</td>\n", "      <td>Medium</td>\n", "      <td><PERSON>_Tones</td>\n", "      <td>Standard_Lens</td>\n", "      <td>Voiceover_With_Music</td>\n", "      <td>25.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Facial</td>\n", "      <td>Wrinkle_Reduction</td>\n", "      <td>2 por 1</td>\n", "      <td>Middle_of_Funnel</td>\n", "      <td>No</td>\n", "      <td>Actual_Treatment_Shown|Before_And_After_Results</td>\n", "      <td><PERSON><PERSON>_Bus<PERSON></td>\n", "      <td>Process_Shot_Treatment_In_Progress</td>\n", "      <td>Relatable_Humor</td>\n", "      <td>No_Text</td>\n", "      <td>...</td>\n", "      <td>3.0</td>\n", "      <td>Clinical_Room</td>\n", "      <td>Bright_High_Key</td>\n", "      <td>Eye_Level</td>\n", "      <td>Medium</td>\n", "      <td>High_Saturation</td>\n", "      <td>Standard_Lens</td>\n", "      <td>Voiceover_With_Music</td>\n", "      <td>35.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["  Strategic_Offer_Architecture.Primary_Treatment_Subject  \\\n", "0                                             Facial       \n", "1                                             Facial       \n", "2                                             Facial       \n", "3                                             Facial       \n", "4                                             Facial       \n", "\n", "  Strategic_Offer_Architecture.Sub_Treatment_Focus  \\\n", "0                                Wrinkle_Reduction   \n", "1                                Wrinkle_Reduction   \n", "2                                Wrinkle_Reduction   \n", "3                                Wrinkle_Reduction   \n", "4                                Wrinkle_Reduction   \n", "\n", "  Strategic_Offer_Architecture.Offer_Structure  \\\n", "0                                      2 por 1   \n", "1                                      2 por 1   \n", "2                                      2 por 1   \n", "3                                      2 por 1   \n", "4                                      2 por 1   \n", "\n", "  Strategic_Offer_Architecture.Ad_Funnel_Stage  \\\n", "0                             Bottom_of_Funnel   \n", "1                             Middle_of_Funnel   \n", "2                             Bottom_of_Funnel   \n", "3                             Middle_of_Funnel   \n", "4                             Middle_of_Funnel   \n", "\n", "  Strategic_Offer_Architecture.CTA_Present  \\\n", "0                                       No   \n", "1                                       No   \n", "2                                      Yes   \n", "3                                       No   \n", "4                                       No   \n", "\n", "            Strategic_Offer_Architecture.Ad_Features  \\\n", "0  Testimonial|Actual_Treatment_Shown|On-Screen_P...   \n", "1                 Testimonial|Actual_Treatment_Shown   \n", "2  Testimonial|Actual_Treatment_Shown|On-Screen_P...   \n", "3   Ad_Recording_Spokesperson|Actual_Treatment_Shown   \n", "4    Actual_Treatment_Shown|Before_And_After_Results   \n", "\n", "  Hook_Deconstruction.Hook_Narrative_Angle  \\\n", "0                      Testimonial_Focused   \n", "1                          Benefit_Focused   \n", "2                          Benefit_Focused   \n", "3                          Problem_Focused   \n", "4                             Myth_Busting   \n", "\n", "  Hook_Deconstruction.Hook_Visual_Subject  \\\n", "0           Extreme_Close_Up_Problem_Area   \n", "1      Process_Shot_Treatment_In_Progress   \n", "2           Extreme_Close_Up_Problem_Area   \n", "3                C<PERSON>_Talking_Head_UGC   \n", "4      Process_Shot_Treatment_In_Progress   \n", "\n", "  Hook_Deconstruction.Hook_Emotional_Tone  \\\n", "0                   Aspirational_Positive   \n", "1                   Aspirational_Positive   \n", "2                   Aspirational_Positive   \n", "3                   Aspirational_Positive   \n", "4                         Relatable_Humor   \n", "\n", "  Hook_Deconstruction.Hook_Text_Type_OCR  ...  \\\n", "0                        Benefit_Promise  ...   \n", "1                                No_Text  ...   \n", "2                         Offer_Headline  ...   \n", "3                         Bold_Statement  ...   \n", "4                                No_Text  ...   \n", "\n", "  Hook_Deconstruction.Hook_Duration_Secs  \\\n", "0                                    5.0   \n", "1                                    4.0   \n", "2                                    5.0   \n", "3                                    5.0   \n", "4                                    3.0   \n", "\n", "  Production_Visual_Aesthetics.Visual_Setting  \\\n", "0                               Clinical_Room   \n", "1                               Clinical_Room   \n", "2                               Clinical_Room   \n", "3                               Clinical_Room   \n", "4                               Clinical_Room   \n", "\n", "  Production_Visual_Aesthetics.Lighting_Style  \\\n", "0                             Bright_High_Key   \n", "1                             Bright_High_Key   \n", "2                             Bright_High_Key   \n", "3                             Bright_High_Key   \n", "4                             Bright_High_Key   \n", "\n", "  Production_Visual_Aesthetics.Camera_Angle  \\\n", "0                                 Eye_Level   \n", "1                                 Eye_Level   \n", "2                                 Eye_Level   \n", "3                                 Eye_Level   \n", "4                                 Eye_Level   \n", "\n", "   Production_Visual_Aesthetics.Editing_Pacing  \\\n", "0                                         Fast   \n", "1                                       Medium   \n", "2                                       Medium   \n", "3                                       Medium   \n", "4                                       Medium   \n", "\n", "  Production_Visual_Aesthetics.Color_Grading_Scheme  \\\n", "0                                   High_Saturation   \n", "1                                        Warm_Tones   \n", "2                                        Warm_Tones   \n", "3                                        Warm_Tones   \n", "4                                   High_Saturation   \n", "\n", "  Production_Visual_Aesthetics.Focal_Length_Style  \\\n", "0                                   Standard_Lens   \n", "1                                   Standard_Lens   \n", "2                                   Standard_Lens   \n", "3                                   Standard_Lens   \n", "4                                   Standard_Lens   \n", "\n", "  Production_Visual_Aesthetics.Sound_Score  \\\n", "0                     Voiceover_With_Music   \n", "1                     Voiceover_With_Music   \n", "2                     Voiceover_With_Music   \n", "3                     Voiceover_With_Music   \n", "4                     Voiceover_With_Music   \n", "\n", "  Production_Visual_Aesthetics.Video_Duration_Secs RESULTADOS  \n", "0                                             24.0        0.0  \n", "1                                             25.0        0.0  \n", "2                                             25.0        0.0  \n", "3                                             25.0        0.0  \n", "4                                             35.0        0.0  \n", "\n", "[5 rows x 24 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["paid_ads.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "c92cb4ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Thigh_Cellulite', 'Glute_Toning/Lifting', 'Spot_Correction|Wrinkle_Reduction', 'Jaw<PERSON>_Sculpting', 'Antiaging', 'General_G<PERSON>', 'Wrinkle_Reduction|Skin_Tightening|General_Glow', 'Wrinkle_Reduction|Spot_Correction', 'Wrinkle_Reduction|Spot_Correction|Skin_Tightening', 'Arm_Flaccidity', 'Wrinkle_Reduction|Spot_Correction|General_Glow', 'Wrinkle_Reduction|Skin_Tightening', 'Wrinkle_Reduction', 'Abdominal_Fat_Reduction|Thigh_Cellulite|Arm_Flaccidity', 'Abdominal_Fat_Reduction|General_Debloating', 'Abdominal_Fat_Reduction|Arm_Flaccidity', 'General_Debloating', 'Skin_Tightening', 'Abdominal_Fat_Reduction', 'Spot_Correction', 'Abdominal_Fat_Reduction|Glute_Toning/Lifting']\n"]}], "source": ["print(get_unique_values(paid_ads, 'Strategic_Offer_Architecture.Sub_Treatment_Focus'))"]}, {"cell_type": "code", "execution_count": null, "id": "82ce1ddc", "metadata": {}, "outputs": [], "source": ["antiaging, spot_correction"]}, {"cell_type": "code", "execution_count": 11, "id": "e2657a42", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Studio_Demonstration', 'Interview_Style', 'UGC_Testimonial', 'Ad_Recording_Spokesperson', 'Voiceover_With_B_Roll', 'Reality_POV', 'Static_Image_Only']\n"]}], "source": ["print(get_unique_values(paid_ads, 'Hook_Deconstruction.Hook_Format_Style'))"]}, {"cell_type": "code", "execution_count": null, "id": "00dfa49e", "metadata": {}, "outputs": [], "source": ["si puedes poner aqui o explicar las siguientes:\n", "* B_Roll_Footage  de Strategic_Offer_Architecture.Ad_Features\n", "* C<PERSON>_Talking_Head_UGC  de Hook_Deconstruction.Hook_Visual_Subject\n", "* UGC_Testimonial, Voiceover_With_B_Roll de Hook_Deconstruction.Hook_Format_Style\n", "* "]}], "metadata": {"kernelspec": {"display_name": "clibel", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}