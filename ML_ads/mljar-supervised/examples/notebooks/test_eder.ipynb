{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ec7531bd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/clibel2/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import pandas as pd\n", "from supervised.automl import AutoML\n", "\n", "from sklearn.metrics import root_mean_squared_error\n", "from sklearn.model_selection import train_test_split\n", "\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import StandardScaler, OneHotEncoder, RobustScaler, OrdinalEncoder\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score"]}, {"cell_type": "code", "execution_count": 2, "id": "08351e68", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv('/home/<USER>/Documents/Clibel/ML_ads/data/ads_ready.csv')\n", "y = df['RESULTADOS']\n", "X = df.drop(columns=['RESULTADOS'])"]}, {"cell_type": "code", "execution_count": 3, "id": "4e6a9e89", "metadata": {}, "outputs": [], "source": ["x_train, x_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)"]}, {"cell_type": "code", "execution_count": 4, "id": "2650d152", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Variance of y_train: 73.33113724499076\n", "Variance of y_test: 51.976569674012865\n"]}], "source": ["# Calculate varices of y_train and y_test\n", "var_y_train = y_train.var(ddof=0)\n", "var_y_test = y_test.var(ddof=0)\n", "print(f\"Variance of y_train: {var_y_train}\")\n", "print(f\"Variance of y_test: {var_y_test}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "3bb26f6f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train data: (2236, 50), (2236,)\n", "Test data: (249, 50), (249,)\n"]}], "source": ["print(f\"Train data: {x_train.shape}, {y_train.shape}\")\n", "print(f\"Test data: {x_test.shape}, {y_test.shape}\")"]}, {"cell_type": "code", "execution_count": null, "id": "964ff9fb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clibel2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}