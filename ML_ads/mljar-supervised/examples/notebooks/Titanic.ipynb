{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Introduction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- This notebook demonstrates how to use mljar auto ml to create a model that predicts which passengers survived the Titanic shipwreck.\n", "- The data is available on [kaggle](https://www.kaggle.com/c/titanic).\n", "![](https://storage.googleapis.com/kaggle-media/welcome/screen1.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Required Packages"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from supervised.automl import AutoML\n", "from sklearn.metrics import accuracy_score\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting basic idea"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["df_train = pd.read_csv(\"./tests/data/Titanic/train.csv\")\n", "df_test = pd.read_csv(\"./tests/data/Titanic/test_with_Survived.csv\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The train data set contains 891 rows and 12 cols\n", "The test data set contains 418 rows and 12 cols\n"]}], "source": ["print(f\"The train data set contains {df_train.shape[0]} rows and {df_train.shape[1]} cols\")\n", "print(f\"The test data set contains {df_test.shape[0]} rows and {df_test.shape[1]} cols\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PassengerId</th>\n", "      <th>Survived</th>\n", "      <th>Pclass</th>\n", "      <th>Name</th>\n", "      <th>Sex</th>\n", "      <th>Age</th>\n", "      <th>SibSp</th>\n", "      <th>Parch</th>\n", "      <th>Ticket</th>\n", "      <th>Fare</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Embarked</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>22.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>A/5 21171</td>\n", "      <td>7.2500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...</td>\n", "      <td>female</td>\n", "      <td>38.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>PC 17599</td>\n", "      <td>71.2833</td>\n", "      <td>C85</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>26.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>STON/O2. 3101282</td>\n", "      <td>7.9250</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   PassengerId  Survived  Pclass  \\\n", "0            1         0       3   \n", "1            2         1       1   \n", "2            3         1       3   \n", "\n", "                                                Name     Sex   Age  SibSp  \\\n", "0                            <PERSON><PERSON>, Mr. <PERSON>    male  22.0      1   \n", "1  C<PERSON>ngs, Mrs. <PERSON> (Florence Briggs Th...  female  38.0      1   \n", "2                             <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female  26.0      0   \n", "\n", "   Parch            Ticket     Fare Cabin Embarked  \n", "0      0         A/5 21171   7.2500   NaN        S  \n", "1      0          PC 17599  71.2833   C85        C  \n", "2      0  STON/O2. 3101282   7.9250   NaN        S  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df_train.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## AutoML \n", "- The `total_time_limit` is the time limit for training a single model, in seconds.\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["X_train,y_train = df_train.drop(['Survived'],axis=1),df_train['Survived']\n", "X_test,y_test = df_test.drop(['Survived'],axis=1),df_test['Survived']\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AutoML directory: AutoML_2\n", "The task is binary_classification with evaluation metric logloss\n", "AutoML will use algorithms: ['Baseline', 'Linear', 'Decision Tree', 'Random Forest', 'Xgboost', 'Neural Network']\n", "AutoML will ensemble availabe models\n", "AutoML steps: ['simple_algorithms', 'default_algorithms', 'ensemble']\n", "* Step simple_algorithms will try to check up to 3 models\n", "1_Baseline logloss 0.666775 trained in 0.21 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Passing parameters norm and vmin/vmax simultaneously is deprecated since 3.3 and will become an error two minor releases later. Please pass vmin/vmax directly to the norm when creating it.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2_DecisionTree logloss 0.648504 trained in 58.35 seconds\n", "Skip default_algorithms because of the time limit.\n", "* Step ensemble will try to check up to 1 model\n", "Ensemble logloss 0.518593 trained in 0.18 seconds\n", "AutoML fit time: 64.94 seconds\n"]}, {"data": {"text/plain": ["AutoML(total_time_limit=10)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["a = AutoML(total_time_limit=10)\n", "a.fit(X_train,y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- It performs automatic EDA, the results are in Markdown report.\n", "- the AutoML results in Markdown report.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extended EDA\n", "To do extended bivariate data analysis of the data, mljar has `extended_eda` method.\n", "This will do the bivariate analysis of features in the dataframe against the passed target feature."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Considering 7 the most frequent values\n", "Considering 7 the most frequent values\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x720 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from supervised.preprocessing.eda import EDA\n", "\n", "EDA.extensive_eda(X_train,y_train,save_path=\"/home/<USER>/mljar-supervised/AutoML_1/\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Make prediction on test "]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["(418,)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["predictions = a.predict(X_test)\n", "predictions.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation "]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model scores 0.758 on kaggle test dataset\n"]}], "source": ["print(f\"Model scores {accuracy_score(y_test,predictions):.3f} on kaggle test dataset\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.12"}}, "nbformat": 4, "nbformat_minor": 4}