{"cells": [{"cell_type": "code", "execution_count": 103, "id": "fe3a8c51", "metadata": {}, "outputs": [], "source": ["fb_obj = {\n", "        \"kind\": \"calendar#freeBusy\",\n", "        \"timeMin\": \"2025-06-10T22:47:59.000Z\",\n", "        \"timeMax\": \"2025-06-17T15:47:59.000Z\",\n", "        \"calendars\": {\n", "            \"<EMAIL>\": {\n", "                \"busy\": [\n", "                    {\n", "                        \"start\": \"2025-06-11T06:30:00.000Z\",\n", "                        \"end\": \"2025-06-11T16:30:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-11T17:00:00.000Z\",\n", "                        \"end\": \"2025-06-11T18:00:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-12T07:00:00.000Z\",\n", "                        \"end\": \"2025-06-12T18:00:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-13T07:00:00.000Z\",\n", "                        \"end\": \"2025-06-13T10:30:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-13T11:00:00.000Z\",\n", "                        \"end\": \"2025-06-13T18:00:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-14T06:30:00.000Z\",\n", "                        \"end\": \"2025-06-14T12:00:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-14T17:00:00.000Z\",\n", "                        \"end\": \"2025-06-14T18:00:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-16T07:00:00.000Z\",\n", "                        \"end\": \"2025-06-16T11:30:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-16T12:00:00.000Z\",\n", "                        \"end\": \"2025-06-16T12:30:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-16T13:00:00.000Z\",\n", "                        \"end\": \"2025-06-16T13:30:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-16T14:00:00.000Z\",\n", "                        \"end\": \"2025-06-16T18:00:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-17T06:30:00.000Z\",\n", "                        \"end\": \"2025-06-17T10:00:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-17T10:30:00.000Z\",\n", "                        \"end\": \"2025-06-17T15:47:59.000Z\"\n", "                    }\n", "                ]\n", "            }\n", "        } \n", "    }\n", "appointment_start_time = \"2025-06-16T14:15:00\"\n"]}, {"cell_type": "code", "execution_count": 104, "id": "539b7604", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "can't compare offset-naive and offset-aware datetimes", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[104]\u001b[39m\u001b[32m, line 75\u001b[39m\n\u001b[32m     73\u001b[39m busy_today = []\n\u001b[32m     74\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m bs, be \u001b[38;5;129;01min\u001b[39;00m busy_slots:\n\u001b[32m---> \u001b[39m\u001b[32m75\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mbe\u001b[49m\u001b[43m \u001b[49m\u001b[43m>\u001b[49m\u001b[43m \u001b[49m\u001b[43mwindow_start_utc\u001b[49m \u001b[38;5;129;01mand\u001b[39;00m bs < window_end_utc:\n\u001b[32m     76\u001b[39m         busy_today.append((\u001b[38;5;28mmax\u001b[39m(bs, window_start_utc), \u001b[38;5;28mmin\u001b[39m(be, window_end_utc)))\n\u001b[32m     77\u001b[39m busy_today.sort(key=\u001b[38;5;28;01mlambda\u001b[39;00m x: x[\u001b[32m0\u001b[39m])\n", "\u001b[31mTypeError\u001b[39m: can't compare offset-naive and offset-aware datetimes"]}], "source": ["# -*- coding: utf-8 -*-\n", "from datetime import datetime, timedelta\n", "\n", "# --- Configuración de zona horaria ---\n", "\n", "# --- Helpers para parsear ISO y formatear con zona ---\n", "\n", "def parse_iso_maybe_z(dt_str):\n", "    \"\"\"\n", "    Parsea cadenas ISO y retorna un datetime aware en UTC.\n", "    Asume que las cadenas sin sufijo Z ya están en UTC.\n", "    \"\"\"\n", "    s = dt_str.strip().replace('\"', '')\n", "    # <PERSON><PERSON>ar sufijo 'Z' si existe\n", "    if s.endswith('Z'):\n", "        s = s[:-1]\n", "    # Intentamos con o sin fracciones de segundo\n", "    for fmt in (\"%Y-%m-%dT%H:%M:%S.%f\", \"%Y-%m-%dT%H:%M:%S\"):\n", "        try:\n", "            naive = datetime.strptime(s, fmt)\n", "            # Asignar UTC\n", "            return UTC.localize(naive)\n", "        except ValueError:\n", "            continue\n", "    raise ValueError(f\"Formato inválido: {dt_str}\")\n", "\n", "def isoformat_madrid(dt_utc):\n", "    \"\"\"\n", "    Convierte un datetime aware en UTC a la hora local de Madrid, suma 2 horas adicionales y formatea como ISO sin offset.\n", "    \"\"\"    \n", "    # Sumar 2 horas ADICIONALES al tiempo ya convertido a Madrid\n", "    dt_local_plus_2 = dt_utc + <PERSON><PERSON><PERSON>(hours=2)\n", "    \n", "    # Formatear sin incluir el offset - solo el tiempo con las 2 horas ya sumadas\n", "    return dt_local_plus_2.strftime(\"%Y-%m-%dT%H:%M:%S\")\n", "\n", "# --- 1) <PERSON><PERSON> inputs ---\n", "# fb_obj: entrada de freeBusy de Google Calendar\n", "# appointment_start_time: string \"YYYY-MM-DDTHH:MM:SS\"\n", "\n", "appointment_dt_utc = parse_iso_maybe_z(appointment_start_time)\n", "\n", "# --- 2) Parsear timeMin / timeMax ---\n", "time_min_utc = parse_iso_maybe_z(fb_obj[\"timeMin\"])\n", "time_max_utc = parse_iso_maybe_z(fb_obj[\"timeMax\"])\n", "\n", "# --- 3) Extraer y convertir franjas 'busy' en UTC-aware ---\n", "cal_entry = list(fb_obj[\"calendars\"].values())[0]\n", "busy_slots = []\n", "for slot in cal_entry.get(\"busy\", []):\n", "    start = parse_iso_maybe_z(slot[\"start\"])\n", "    end   = parse_iso_maybe_z(slot[\"end\"])\n", "    busy_slots.append((start, end))\n", "\n", "# --- 4) Determinar rango de fechas en hora local Madrid ---\n", "start_local = time_min_utc.date()\n", "end_local   = time_max_utc.date()\n", "\n", "output = []\n", "current_date = start_local\n", "\n", "while current_date <= end_local:\n", "    # Exc<PERSON>ir domingos (weekday()==6 → domingo)\n", "    if current_date.weekday() != 6:\n", "        # Ventana diaria 08:00–19:00 en hora local Madrid\n", "        window_start_local = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=6) # Esta dos horas por debajo lo que nos devuelve Google Calendar\n", "        window_end_local   = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=17)\n", "        # Convertir ventana a UTC para comparar slots ocupados\n", "        window_start_utc = window_start_local\n", "        window_end_utc   = window_end_local\n", "\n", "        # Filtrar franjas busy que se solapen con ventana UTC\n", "        busy_today = []\n", "        for bs, be in busy_slots:\n", "            if be > window_start_utc and bs < window_end_utc:\n", "                busy_today.append((max(bs, window_start_utc), min(be, window_end_utc)))\n", "        busy_today.sort(key=lambda x: x[0])\n", "\n", "        # Calcular huecos libres en UTC y luego convertirlos a hora local\n", "        frees = []\n", "        cursor = window_start_utc\n", "        for bs, be in busy_today:\n", "            if bs > cursor:\n", "                frees.append({\n", "                    \"start\": isoformat_madrid(cursor),\n", "                    \"end\":   isoformat_madrid(bs)\n", "                })\n", "            cursor = max(cursor, be)\n", "        if cursor < window_end_utc:\n", "            frees.append({\n", "                \"start\": isoformat_madrid(cursor),\n", "                \"end\":   isoformat_madrid(window_end_utc)\n", "            })\n", "\n", "        output.append({\n", "            \"date\": current_date.isoformat(),\n", "            \"free_slots\": frees\n", "        })\n", "    current_date += <PERSON><PERSON><PERSON>(days=1)\n", "\n", "# --- 5) Verificar cita existente en hora local Madrid ---\n", "appointment_local = appointment_dt_utc\n", "interval_end_local = appointment_local + <PERSON><PERSON><PERSON>(minutes=30)\n", "\n", "change_appointment = True\n", "for day in output:\n", "    if day[\"date\"] == appointment_local.date().isoformat():\n", "        for fs in day[\"free_slots\"]:\n", "            # Parsear y convertir ISO a aware Madrid\n", "            fs_start = datetime.fromisoformat(fs[\"start\"])\n", "            fs_end   = datetime.fromisoformat(fs[\"end\"])\n", "            if fs_start <= appointment_local < fs_end and interval_end_local <= fs_end:\n", "                change_appointment = False\n", "                break\n", "        break\n", "\n", "# --- 6) Resultado global ---\n", "result = {\n", "    \"data\": output,\n", "    \"change_appointment\": change_appointment\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "id": "2b4c1dc2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OG: 2025-06-11 08:15:00+02:00\n", "2025-06-11 10:15:00+02:00\n", "OG: 2025-06-11 09:19:13+02:00\n", "2025-06-11 11:19:13+02:00\n", "OG: 2025-06-11 18:30:00+02:00\n", "2025-06-11 20:30:00+02:00\n", "OG: 2025-06-11 19:00:00+02:00\n", "2025-06-11 21:00:00+02:00\n", "OG: 2025-06-12 08:15:00+02:00\n", "2025-06-12 10:15:00+02:00\n", "OG: 2025-06-12 09:00:00+02:00\n", "2025-06-12 11:00:00+02:00\n", "OG: 2025-06-13 08:15:00+02:00\n", "2025-06-13 10:15:00+02:00\n", "OG: 2025-06-13 09:00:00+02:00\n", "2025-06-13 11:00:00+02:00\n", "OG: 2025-06-13 12:30:00+02:00\n", "2025-06-13 14:30:00+02:00\n", "OG: 2025-06-13 13:00:00+02:00\n", "2025-06-13 15:00:00+02:00\n", "OG: 2025-06-14 08:15:00+02:00\n", "2025-06-14 10:15:00+02:00\n", "OG: 2025-06-14 08:30:00+02:00\n", "2025-06-14 10:30:00+02:00\n", "OG: 2025-06-14 14:00:00+02:00\n", "2025-06-14 16:00:00+02:00\n", "OG: 2025-06-14 19:00:00+02:00\n", "2025-06-14 21:00:00+02:00\n", "OG: 2025-06-16 08:15:00+02:00\n", "2025-06-16 10:15:00+02:00\n", "OG: 2025-06-16 09:00:00+02:00\n", "2025-06-16 11:00:00+02:00\n", "OG: 2025-06-16 13:30:00+02:00\n", "2025-06-16 15:30:00+02:00\n", "OG: 2025-06-16 14:00:00+02:00\n", "2025-06-16 16:00:00+02:00\n", "OG: 2025-06-16 14:30:00+02:00\n", "2025-06-16 16:30:00+02:00\n", "OG: 2025-06-16 15:00:00+02:00\n", "2025-06-16 17:00:00+02:00\n", "OG: 2025-06-16 15:30:00+02:00\n", "2025-06-16 17:30:00+02:00\n", "OG: 2025-06-16 17:00:00+02:00\n", "2025-06-16 19:00:00+02:00\n", "OG: 2025-06-17 08:15:00+02:00\n", "2025-06-17 10:15:00+02:00\n", "OG: 2025-06-17 08:30:00+02:00\n", "2025-06-17 10:30:00+02:00\n", "OG: 2025-06-17 12:00:00+02:00\n", "2025-06-17 14:00:00+02:00\n", "OG: 2025-06-17 12:30:00+02:00\n", "2025-06-17 14:30:00+02:00\n", "OG: 2025-06-18 08:15:00+02:00\n", "2025-06-18 10:15:00+02:00\n", "OG: 2025-06-18 09:00:00+02:00\n", "2025-06-18 11:00:00+02:00\n", "OG: 2025-06-18 11:30:00+02:00\n", "2025-06-18 13:30:00+02:00\n", "OG: 2025-06-18 13:00:00+02:00\n", "2025-06-18 15:00:00+02:00\n", "OG: 2025-06-18 15:30:00+02:00\n", "2025-06-18 17:30:00+02:00\n", "OG: 2025-06-18 16:00:00+02:00\n", "2025-06-18 18:00:00+02:00\n", "OG: 2025-06-19 08:15:00+02:00\n", "2025-06-19 10:15:00+02:00\n", "OG: 2025-06-19 09:30:00+02:00\n", "2025-06-19 11:30:00+02:00\n", "OG: 2025-06-19 10:30:00+02:00\n", "2025-06-19 12:30:00+02:00\n", "OG: 2025-06-19 11:00:00+02:00\n", "2025-06-19 13:00:00+02:00\n", "OG: 2025-06-19 14:30:00+02:00\n", "2025-06-19 16:30:00+02:00\n", "OG: 2025-06-19 15:00:00+02:00\n", "2025-06-19 17:00:00+02:00\n", "OG: 2025-06-20 08:15:00+02:00\n", "2025-06-20 10:15:00+02:00\n", "OG: 2025-06-20 09:00:00+02:00\n", "2025-06-20 11:00:00+02:00\n", "OG: 2025-06-20 13:00:00+02:00\n", "2025-06-20 15:00:00+02:00\n", "OG: 2025-06-20 14:00:00+02:00\n", "2025-06-20 16:00:00+02:00\n", "OG: 2025-06-20 15:00:00+02:00\n", "2025-06-20 17:00:00+02:00\n", "OG: 2025-06-20 16:00:00+02:00\n", "2025-06-20 18:00:00+02:00\n", "OG: 2025-06-20 18:00:00+02:00\n", "2025-06-20 20:00:00+02:00\n", "OG: 2025-06-20 19:00:00+02:00\n", "2025-06-20 21:00:00+02:00\n", "OG: 2025-06-21 08:15:00+02:00\n", "2025-06-21 10:15:00+02:00\n", "OG: 2025-06-21 09:30:00+02:00\n", "2025-06-21 11:30:00+02:00\n", "OG: 2025-06-21 10:30:00+02:00\n", "2025-06-21 12:30:00+02:00\n", "OG: 2025-06-21 11:00:00+02:00\n", "2025-06-21 13:00:00+02:00\n", "OG: 2025-06-21 14:00:00+02:00\n", "2025-06-21 16:00:00+02:00\n", "OG: 2025-06-21 19:15:00+02:00\n", "2025-06-21 21:15:00+02:00\n", "OG: 2025-06-23 08:15:00+02:00\n", "2025-06-23 10:15:00+02:00\n", "OG: 2025-06-23 09:00:00+02:00\n", "2025-06-23 11:00:00+02:00\n", "OG: 2025-06-23 09:30:00+02:00\n", "2025-06-23 11:30:00+02:00\n", "OG: 2025-06-23 10:00:00+02:00\n", "2025-06-23 12:00:00+02:00\n", "OG: 2025-06-23 12:00:00+02:00\n", "2025-06-23 14:00:00+02:00\n", "OG: 2025-06-23 12:30:00+02:00\n", "2025-06-23 14:30:00+02:00\n", "OG: 2025-06-23 13:00:00+02:00\n", "2025-06-23 15:00:00+02:00\n", "OG: 2025-06-23 13:30:00+02:00\n", "2025-06-23 15:30:00+02:00\n", "OG: 2025-06-23 14:30:00+02:00\n", "2025-06-23 16:30:00+02:00\n", "OG: 2025-06-23 15:00:00+02:00\n", "2025-06-23 17:00:00+02:00\n", "OG: 2025-06-23 19:00:00+02:00\n", "2025-06-23 21:00:00+02:00\n", "OG: 2025-06-23 19:15:00+02:00\n", "2025-06-23 21:15:00+02:00\n", "OG: 2025-06-24 08:15:00+02:00\n", "2025-06-24 10:15:00+02:00\n", "OG: 2025-06-24 19:15:00+02:00\n", "2025-06-24 21:15:00+02:00\n", "OG: 2025-06-25 08:15:00+02:00\n", "2025-06-25 10:15:00+02:00\n", "OG: 2025-06-25 19:15:00+02:00\n", "2025-06-25 21:15:00+02:00\n", "OG: 2025-06-26 08:15:00+02:00\n", "2025-06-26 10:15:00+02:00\n", "OG: 2025-06-26 12:30:00+02:00\n", "2025-06-26 14:30:00+02:00\n", "OG: 2025-06-26 13:00:00+02:00\n", "2025-06-26 15:00:00+02:00\n", "OG: 2025-06-26 19:15:00+02:00\n", "2025-06-26 21:15:00+02:00\n", "OG: 2025-06-27 08:15:00+02:00\n", "2025-06-27 10:15:00+02:00\n", "OG: 2025-06-27 19:15:00+02:00\n", "2025-06-27 21:15:00+02:00\n"]}], "source": ["# -*- coding: utf-8 -*-\n", "from datetime import datetime, timedelta\n", "import pytz  # pip install pytz\n", "\n", "# --- Configuración de zona horaria ---\n", "UTC = pytz.UTC\n", "MADRID = pytz.timezone(\"Europe/Madrid\")\n", "\n", "# --- Helpers para parsear ISO y formatear con zona ---\n", "\n", "def parse_iso_maybe_z(dt_str):\n", "    \"\"\"\n", "    Parsea cadenas ISO y retorna un datetime aware en UTC.\n", "    Asume que las cadenas sin sufijo Z ya están en UTC.\n", "    \"\"\"\n", "    s = dt_str.strip().replace('\"', '')\n", "    # <PERSON><PERSON>ar sufijo 'Z' si existe\n", "    if s.endswith('Z'):\n", "        s = s[:-1]\n", "    # Intentamos con o sin fracciones de segundo\n", "    for fmt in (\"%Y-%m-%dT%H:%M:%S.%f\", \"%Y-%m-%dT%H:%M:%S\"):\n", "        try:\n", "            naive = datetime.strptime(s, fmt)\n", "            # Asignar UTC\n", "            return UTC.localize(naive)\n", "        except ValueError:\n", "            continue\n", "    raise ValueError(f\"Formato inválido: {dt_str}\")\n", "\n", "def isoformat_madrid(dt_utc):\n", "    \"\"\"\n", "    Convierte un datetime aware en UTC a la hora local de Madrid y formatea como ISO sin offset.\n", "    \"\"\"\n", "    # Convertir de UTC a Madrid\n", "    dt_local = dt_utc.astimezone(MADRID)\n", "    print(f\"OG: {dt_local}\")\n", "    dt_local = dt_local + timedelta(hours=2)  # ERROR: trying to add timedelta to STRING\n", "    print(f\"{dt_local}\")\n", "    # Formatear sin incluir el offset\n", "    return dt_local.strftime(\"%Y-%m-%dT%H:%M:%S\")\n", "\n", "# --- 1) <PERSON><PERSON> inputs ---\n", "# fb_obj: entrada de freeBusy de Google Calendar\n", "# appointment_start_time: string \"YYYY-MM-DDTHH:MM:SS\"\n", "\n", "appointment_dt_utc = parse_iso_maybe_z(appointment_start_time)\n", "\n", "# --- 2) Parsear timeMin / timeMax ---\n", "time_min_utc = parse_iso_maybe_z(fb_obj[\"timeMin\"])\n", "time_max_utc = parse_iso_maybe_z(fb_obj[\"timeMax\"])\n", "\n", "# --- 3) Extraer y convertir franjas 'busy' en UTC-aware ---\n", "cal_entry = list(fb_obj[\"calendars\"].values())[0]\n", "busy_slots = []\n", "for slot in cal_entry.get(\"busy\", []):\n", "    start = parse_iso_maybe_z(slot[\"start\"])\n", "    end   = parse_iso_maybe_z(slot[\"end\"])\n", "    busy_slots.append((start, end))\n", "\n", "# --- 4) Determinar rango de fechas en hora local Madrid ---\n", "start_local = time_min_utc.astimezone(MADRID).date()\n", "end_local   = time_max_utc.astimezone(MADRID).date()\n", "\n", "output = []\n", "current_date = start_local\n", "\n", "while current_date <= end_local:\n", "    # Exc<PERSON>ir domingos (weekday()==6 → domingo)\n", "    if current_date.weekday() != 6:\n", "        # Ventana diaria 08:00–19:00 en hora local Madrid\n", "        window_start_local = datetime.combine(current_date, datetime.min.time(), tzinfo=MADRID) + timedelta(hours=6) # Esta dos horas por debajo lo que nos devuelve Google Calendar\n", "        window_end_local   = datetime.combine(current_date, datetime.min.time(), tzinfo=MADRID) + timedelta(hours=17)\n", "        # Convertir ventana a UTC para comparar slots ocupados\n", "        window_start_utc = window_start_local.astimezone(UTC)\n", "        window_end_utc   = window_end_local.astimezone(UTC)\n", "\n", "        # Filtrar franjas busy que se solapen con ventana UTC\n", "        busy_today = []\n", "        for bs, be in busy_slots:\n", "            if be > window_start_utc and bs < window_end_utc:\n", "                busy_today.append((max(bs, window_start_utc), min(be, window_end_utc)))\n", "        busy_today.sort(key=lambda x: x[0])\n", "\n", "        # Calcular huecos libres en UTC y luego convertirlos a hora local\n", "        frees = []\n", "        cursor = window_start_utc\n", "        for bs, be in busy_today:\n", "            if bs > cursor:\n", "                frees.append({\n", "                    \"start\": isoformat_madrid(cursor),\n", "                    \"end\":   isoformat_madrid(bs)\n", "                })\n", "            cursor = max(cursor, be)\n", "        if cursor < window_end_utc:\n", "            frees.append({\n", "                \"start\": isoformat_madrid(cursor),\n", "                \"end\":   isoformat_madrid(window_end_utc)\n", "            })\n", "\n", "        output.append({\n", "            \"date\": current_date.isoformat(),\n", "            \"free_slots\": frees\n", "        })\n", "    current_date += <PERSON><PERSON><PERSON>(days=1)\n", "\n", "# --- 5) Verificar cita existente en hora local Madrid ---\n", "appointment_local = appointment_dt_utc.astimezone(MADRID)\n", "interval_end_local = appointment_local + <PERSON><PERSON><PERSON>(minutes=30)\n", "\n", "change_appointment = True\n", "for day in output:\n", "    if day[\"date\"] == appointment_local.date().isoformat():\n", "        for fs in day[\"free_slots\"]:\n", "            # Parsear y convertir ISO a aware Madrid\n", "            fs_start = datetime.fromisoformat(fs[\"start\"]).astimezone(MADRID)\n", "            fs_end   = datetime.fromisoformat(fs[\"end\"]).astimezone(MADRID)\n", "            if fs_start <= appointment_local < fs_end and interval_end_local <= fs_end:\n", "                change_appointment = False\n", "                break\n", "        break\n", "\n", "# --- 6) Resultado global ---\n", "result = {\n", "    \"data\": output,\n", "    \"change_appointment\": change_appointment\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "id": "430e5ec9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OG: 2025-06-11 08:15:00+02:00\n", "2025-06-11 10:15:00+02:00\n", "OG: 2025-06-11 09:19:13+02:00\n", "2025-06-11 11:19:13+02:00\n", "OG: 2025-06-11 18:30:00+02:00\n", "2025-06-11 20:30:00+02:00\n", "OG: 2025-06-11 19:00:00+02:00\n", "2025-06-11 21:00:00+02:00\n", "OG: 2025-06-12 08:15:00+02:00\n", "2025-06-12 10:15:00+02:00\n", "OG: 2025-06-12 09:00:00+02:00\n", "2025-06-12 11:00:00+02:00\n", "OG: 2025-06-13 08:15:00+02:00\n", "2025-06-13 10:15:00+02:00\n", "OG: 2025-06-13 09:00:00+02:00\n", "2025-06-13 11:00:00+02:00\n", "OG: 2025-06-13 12:30:00+02:00\n", "2025-06-13 14:30:00+02:00\n", "OG: 2025-06-13 13:00:00+02:00\n", "2025-06-13 15:00:00+02:00\n", "OG: 2025-06-14 08:15:00+02:00\n", "2025-06-14 10:15:00+02:00\n", "OG: 2025-06-14 08:30:00+02:00\n", "2025-06-14 10:30:00+02:00\n", "OG: 2025-06-14 14:00:00+02:00\n", "2025-06-14 16:00:00+02:00\n", "OG: 2025-06-14 19:00:00+02:00\n", "2025-06-14 21:00:00+02:00\n", "OG: 2025-06-16 08:15:00+02:00\n", "2025-06-16 10:15:00+02:00\n", "OG: 2025-06-16 09:00:00+02:00\n", "2025-06-16 11:00:00+02:00\n", "OG: 2025-06-16 13:30:00+02:00\n", "2025-06-16 15:30:00+02:00\n", "OG: 2025-06-16 14:00:00+02:00\n", "2025-06-16 16:00:00+02:00\n", "OG: 2025-06-16 14:30:00+02:00\n", "2025-06-16 16:30:00+02:00\n", "OG: 2025-06-16 15:00:00+02:00\n", "2025-06-16 17:00:00+02:00\n", "OG: 2025-06-16 15:30:00+02:00\n", "2025-06-16 17:30:00+02:00\n", "OG: 2025-06-16 17:00:00+02:00\n", "2025-06-16 19:00:00+02:00\n", "OG: 2025-06-17 08:15:00+02:00\n", "2025-06-17 10:15:00+02:00\n", "OG: 2025-06-17 08:30:00+02:00\n", "2025-06-17 10:30:00+02:00\n", "OG: 2025-06-17 12:00:00+02:00\n", "2025-06-17 14:00:00+02:00\n", "OG: 2025-06-17 12:30:00+02:00\n", "2025-06-17 14:30:00+02:00\n", "OG: 2025-06-18 08:15:00+02:00\n", "2025-06-18 10:15:00+02:00\n", "OG: 2025-06-18 09:00:00+02:00\n", "2025-06-18 11:00:00+02:00\n", "OG: 2025-06-18 11:30:00+02:00\n", "2025-06-18 13:30:00+02:00\n", "OG: 2025-06-18 13:00:00+02:00\n", "2025-06-18 15:00:00+02:00\n", "OG: 2025-06-18 15:30:00+02:00\n", "2025-06-18 17:30:00+02:00\n", "OG: 2025-06-18 16:00:00+02:00\n", "2025-06-18 18:00:00+02:00\n", "OG: 2025-06-19 08:15:00+02:00\n", "2025-06-19 10:15:00+02:00\n", "OG: 2025-06-19 09:30:00+02:00\n", "2025-06-19 11:30:00+02:00\n", "OG: 2025-06-19 10:30:00+02:00\n", "2025-06-19 12:30:00+02:00\n", "OG: 2025-06-19 11:00:00+02:00\n", "2025-06-19 13:00:00+02:00\n", "OG: 2025-06-19 14:30:00+02:00\n", "2025-06-19 16:30:00+02:00\n", "OG: 2025-06-19 15:00:00+02:00\n", "2025-06-19 17:00:00+02:00\n", "OG: 2025-06-20 08:15:00+02:00\n", "2025-06-20 10:15:00+02:00\n", "OG: 2025-06-20 09:00:00+02:00\n", "2025-06-20 11:00:00+02:00\n", "OG: 2025-06-20 13:00:00+02:00\n", "2025-06-20 15:00:00+02:00\n", "OG: 2025-06-20 14:00:00+02:00\n", "2025-06-20 16:00:00+02:00\n", "OG: 2025-06-20 15:00:00+02:00\n", "2025-06-20 17:00:00+02:00\n", "OG: 2025-06-20 16:00:00+02:00\n", "2025-06-20 18:00:00+02:00\n", "OG: 2025-06-20 18:00:00+02:00\n", "2025-06-20 20:00:00+02:00\n", "OG: 2025-06-20 19:00:00+02:00\n", "2025-06-20 21:00:00+02:00\n", "OG: 2025-06-21 08:15:00+02:00\n", "2025-06-21 10:15:00+02:00\n", "OG: 2025-06-21 09:30:00+02:00\n", "2025-06-21 11:30:00+02:00\n", "OG: 2025-06-21 10:30:00+02:00\n", "2025-06-21 12:30:00+02:00\n", "OG: 2025-06-21 11:00:00+02:00\n", "2025-06-21 13:00:00+02:00\n", "OG: 2025-06-21 14:00:00+02:00\n", "2025-06-21 16:00:00+02:00\n", "OG: 2025-06-21 19:15:00+02:00\n", "2025-06-21 21:15:00+02:00\n", "OG: 2025-06-23 08:15:00+02:00\n", "2025-06-23 10:15:00+02:00\n", "OG: 2025-06-23 09:00:00+02:00\n", "2025-06-23 11:00:00+02:00\n", "OG: 2025-06-23 09:30:00+02:00\n", "2025-06-23 11:30:00+02:00\n", "OG: 2025-06-23 10:00:00+02:00\n", "2025-06-23 12:00:00+02:00\n", "OG: 2025-06-23 12:00:00+02:00\n", "2025-06-23 14:00:00+02:00\n", "OG: 2025-06-23 12:30:00+02:00\n", "2025-06-23 14:30:00+02:00\n", "OG: 2025-06-23 13:00:00+02:00\n", "2025-06-23 15:00:00+02:00\n", "OG: 2025-06-23 13:30:00+02:00\n", "2025-06-23 15:30:00+02:00\n", "OG: 2025-06-23 14:30:00+02:00\n", "2025-06-23 16:30:00+02:00\n", "OG: 2025-06-23 15:00:00+02:00\n", "2025-06-23 17:00:00+02:00\n", "OG: 2025-06-23 19:00:00+02:00\n", "2025-06-23 21:00:00+02:00\n", "OG: 2025-06-23 19:15:00+02:00\n", "2025-06-23 21:15:00+02:00\n", "OG: 2025-06-24 08:15:00+02:00\n", "2025-06-24 10:15:00+02:00\n", "OG: 2025-06-24 19:15:00+02:00\n", "2025-06-24 21:15:00+02:00\n", "OG: 2025-06-25 08:15:00+02:00\n", "2025-06-25 10:15:00+02:00\n", "OG: 2025-06-25 19:15:00+02:00\n", "2025-06-25 21:15:00+02:00\n", "OG: 2025-06-26 08:15:00+02:00\n", "2025-06-26 10:15:00+02:00\n", "OG: 2025-06-26 12:30:00+02:00\n", "2025-06-26 14:30:00+02:00\n", "OG: 2025-06-26 13:00:00+02:00\n", "2025-06-26 15:00:00+02:00\n", "OG: 2025-06-26 19:15:00+02:00\n", "2025-06-26 21:15:00+02:00\n", "OG: 2025-06-27 08:15:00+02:00\n", "2025-06-27 10:15:00+02:00\n", "OG: 2025-06-27 19:15:00+02:00\n", "2025-06-27 21:15:00+02:00\n"]}], "source": ["# -*- coding: utf-8 -*-\n", "from datetime import datetime, timedelta\n", "import pytz  # pip install pytz\n", "\n", "# --- Configuración de zona horaria ---\n", "UTC = pytz.UTC\n", "MADRID = pytz.timezone(\"Europe/Madrid\")\n", "\n", "# --- Helpers para parsear ISO y formatear con zona ---\n", "\n", "def parse_iso_maybe_z(dt_str):\n", "    \"\"\"\n", "    Parsea cadenas ISO y retorna un datetime aware en UTC.\n", "    Asume que las cadenas sin sufijo Z ya están en UTC.\n", "    \"\"\"\n", "    s = dt_str.strip().replace('\"', '')\n", "    # <PERSON><PERSON>ar sufijo 'Z' si existe\n", "    if s.endswith('Z'):\n", "        s = s[:-1]\n", "    # Intentamos con o sin fracciones de segundo\n", "    for fmt in (\"%Y-%m-%dT%H:%M:%S.%f\", \"%Y-%m-%dT%H:%M:%S\"):\n", "        try:\n", "            naive = datetime.strptime(s, fmt)\n", "            # Asignar UTC\n", "            return UTC.localize(naive)\n", "        except ValueError:\n", "            continue\n", "    raise ValueError(f\"Formato inválido: {dt_str}\")\n", "\n", "def isoformat_madrid(dt_utc):\n", "    \"\"\"\n", "    Convierte un datetime aware en UTC a la hora local de Madrid y formatea como ISO sin offset.\n", "    \"\"\"\n", "    # Convertir de UTC a Madrid\n", "    dt_local = dt_utc.astimezone(MADRID)\n", "    print(f\"OG: {dt_local}\")\n", "    dt_local = dt_local + timedelta(hours=2)  # ERROR: trying to add timedelta to STRING\n", "    print(f\"{dt_local}\")\n", "    # Formatear sin incluir el offset\n", "    return dt_local.strftime(\"%Y-%m-%dT%H:%M:%S\")\n", "\n", "# --- 1) <PERSON><PERSON> inputs ---\n", "# fb_obj: entrada de freeBusy de Google Calendar\n", "# appointment_start_time: string \"YYYY-MM-DDTHH:MM:SS\"\n", "\n", "appointment_dt_utc = parse_iso_maybe_z(appointment_start_time)\n", "\n", "# --- 2) Parsear timeMin / timeMax ---\n", "time_min_utc = parse_iso_maybe_z(fb_obj[\"timeMin\"])\n", "time_max_utc = parse_iso_maybe_z(fb_obj[\"timeMax\"])\n", "\n", "# --- 3) Extraer y convertir franjas 'busy' en UTC-aware ---\n", "cal_entry = list(fb_obj[\"calendars\"].values())[0]\n", "busy_slots = []\n", "for slot in cal_entry.get(\"busy\", []):\n", "    start = parse_iso_maybe_z(slot[\"start\"])\n", "    end   = parse_iso_maybe_z(slot[\"end\"])\n", "    busy_slots.append((start, end))\n", "\n", "# --- 4) Determinar rango de fechas en hora local Madrid ---\n", "start_local = time_min_utc.astimezone(MADRID).date()\n", "end_local   = time_max_utc.astimezone(MADRID).date()\n", "\n", "output = []\n", "current_date = start_local\n", "\n", "while current_date <= end_local:\n", "    # Exc<PERSON>ir domingos (weekday()==6 → domingo)\n", "    if current_date.weekday() != 6:\n", "        # Ventana diaria 08:00–19:00 en hora local Madrid\n", "        window_start_local = datetime.combine(current_date, datetime.min.time(), tzinfo=MADRID) + timedelta(hours=6) # Esta dos horas por debajo lo que nos devuelve Google Calendar\n", "        window_end_local   = datetime.combine(current_date, datetime.min.time(), tzinfo=MADRID) + timedelta(hours=17)\n", "        # Convertir ventana a UTC para comparar slots ocupados\n", "        window_start_utc = window_start_local.astimezone(UTC)\n", "        window_end_utc   = window_end_local.astimezone(UTC)\n", "\n", "        # Filtrar franjas busy que se solapen con ventana UTC\n", "        busy_today = []\n", "        for bs, be in busy_slots:\n", "            if be > window_start_utc and bs < window_end_utc:\n", "                busy_today.append((max(bs, window_start_utc), min(be, window_end_utc)))\n", "        busy_today.sort(key=lambda x: x[0])\n", "\n", "        # Calcular huecos libres en UTC y luego convertirlos a hora local\n", "        frees = []\n", "        cursor = window_start_utc\n", "        for bs, be in busy_today:\n", "            if bs > cursor:\n", "                frees.append({\n", "                    \"start\": isoformat_madrid(cursor),\n", "                    \"end\":   isoformat_madrid(bs)\n", "                })\n", "            cursor = max(cursor, be)\n", "        if cursor < window_end_utc:\n", "            frees.append({\n", "                \"start\": isoformat_madrid(cursor),\n", "                \"end\":   isoformat_madrid(window_end_utc)\n", "            })\n", "\n", "        output.append({\n", "            \"date\": current_date.isoformat(),\n", "            \"free_slots\": frees\n", "        })\n", "    current_date += <PERSON><PERSON><PERSON>(days=1)\n", "\n", "# --- 5) Verificar cita existente en hora local Madrid ---\n", "appointment_local = appointment_dt_utc.astimezone(MADRID)\n", "interval_end_local = appointment_local + <PERSON><PERSON><PERSON>(minutes=30)\n", "\n", "change_appointment = True\n", "for day in output:\n", "    if day[\"date\"] == appointment_local.date().isoformat():\n", "        for fs in day[\"free_slots\"]:\n", "            # Parsear y convertir ISO a aware Madrid\n", "            fs_start = datetime.fromisoformat(fs[\"start\"]).astimezone(MADRID)\n", "            fs_end   = datetime.fromisoformat(fs[\"end\"]).astimezone(MADRID)\n", "            if fs_start <= appointment_local < fs_end and interval_end_local <= fs_end:\n", "                change_appointment = False\n", "                break\n", "        break\n", "\n", "# --- 6) Resultado global ---\n", "result = {\n", "    \"data\": output,\n", "    \"change_appointment\": change_appointment\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "id": "cc9c589a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'data': [{'date': '2025-06-11',\n", "   'free_slots': [{'start': '2025-06-11T10:15:00',\n", "     'end': '2025-06-11T10:30:00'},\n", "    {'start': '2025-06-11T20:30:00', 'end': '2025-06-11T21:00:00'}]},\n", "  {'date': '2025-06-12',\n", "   'free_slots': [{'start': '2025-06-12T10:15:00',\n", "     'end': '2025-06-12T11:00:00'}]},\n", "  {'date': '2025-06-13',\n", "   'free_slots': [{'start': '2025-06-13T10:15:00',\n", "     'end': '2025-06-13T11:00:00'},\n", "    {'start': '2025-06-13T14:30:00', 'end': '2025-06-13T15:00:00'}]},\n", "  {'date': '2025-06-14',\n", "   'free_slots': [{'start': '2025-06-14T10:15:00',\n", "     'end': '2025-06-14T10:30:00'},\n", "    {'start': '2025-06-14T16:00:00', 'end': '2025-06-14T21:00:00'}]},\n", "  {'date': '2025-06-16',\n", "   'free_slots': [{'start': '2025-06-16T10:15:00',\n", "     'end': '2025-06-16T11:00:00'},\n", "    {'start': '2025-06-16T15:30:00', 'end': '2025-06-16T16:00:00'},\n", "    {'start': '2025-06-16T16:30:00', 'end': '2025-06-16T17:00:00'},\n", "    {'start': '2025-06-16T17:30:00', 'end': '2025-06-16T18:00:00'}]},\n", "  {'date': '2025-06-17',\n", "   'free_slots': [{'start': '2025-06-17T10:15:00',\n", "     'end': '2025-06-17T10:30:00'},\n", "    {'start': '2025-06-17T14:00:00', 'end': '2025-06-17T14:30:00'},\n", "    {'start': '2025-06-17T19:47:59', 'end': '2025-06-17T21:15:00'}]}],\n", " 'change_appointment': True}"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}], "metadata": {"kernelspec": {"display_name": "clibel", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}