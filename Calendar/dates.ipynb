fb_obj = {
        "kind": "calendar#freeBusy",
        "timeMin": "2025-06-11T13:46:52.000Z",
        "timeMax": "2025-06-18T06:46:52.000Z",
        "calendars": {
            "<EMAIL>": {
                "busy": [
                    {
                        "start": "2025-06-11T13:46:52.000Z",
                        "end": "2025-06-11T16:30:00.000Z"
                    },
                    {
                        "start": "2025-06-11T17:00:00.000Z",
                        "end": "2025-06-11T18:00:00.000Z"
                    },
                    {
                        "start": "2025-06-12T07:00:00.000Z",
                        "end": "2025-06-12T18:00:00.000Z"
                    },
                    {
                        "start": "2025-06-13T07:00:00.000Z",
                        "end": "2025-06-13T10:30:00.000Z"
                    },
                    {
                        "start": "2025-06-13T11:00:00.000Z",
                        "end": "2025-06-13T18:00:00.000Z"
                    },
                    {
                        "start": "2025-06-14T06:30:00.000Z",
                        "end": "2025-06-14T12:00:00.000Z"
                    },
                    {
                        "start": "2025-06-14T17:00:00.000Z",
                        "end": "2025-06-14T18:00:00.000Z"
                    },
                    {
                        "start": "2025-06-16T07:00:00.000Z",
                        "end": "2025-06-16T11:30:00.000Z"
                    },
                    {
                        "start": "2025-06-16T12:00:00.000Z",
                        "end": "2025-06-16T12:30:00.000Z"
                    },
                    {
                        "start": "2025-06-16T13:00:00.000Z",
                        "end": "2025-06-16T13:30:00.000Z"
                    },
                    {
                        "start": "2025-06-16T14:00:00.000Z",
                        "end": "2025-06-16T18:00:00.000Z"
                    },
                    {
                        "start": "2025-06-17T06:30:00.000Z",
                        "end": "2025-06-17T10:00:00.000Z"
                    },
                    {
                        "start": "2025-06-17T10:30:00.000Z",
                        "end": "2025-06-17T18:00:00.000Z"
                    }
                ]
            }
        }
    }
appointment_start_time = "2025-06-16T14:15:00"


# -*- coding: utf-8 -*-
from datetime import datetime, timedelta
import pytz  # pip install pytz

# --- Configuración de zona horaria ---
UTC = pytz.UTC
MADRID = pytz.timezone("Europe/Madrid")

# --- Helpers para parsear ISO y formatear con zona ---

def parse_iso_maybe_z(dt_str):
    """
    Parsea cadenas ISO y retorna un datetime aware en UTC.
    Asume que las cadenas sin sufijo Z ya están en UTC.
    """
    s = dt_str.strip().replace('"', '')
    # Quitar sufijo 'Z' si existe
    if s.endswith('Z'):
        s = s[:-1]
    # Intentamos con o sin fracciones de segundo
    for fmt in ("%Y-%m-%dT%H:%M:%S.%f", "%Y-%m-%dT%H:%M:%S"):
        try:
            naive = datetime.strptime(s, fmt)
            # Asignar UTC
            return UTC.localize(naive)
        except ValueError:
            continue
    raise ValueError(f"Formato inválido: {dt_str}")

def isoformat_madrid(dt_input):
    """
    Suma 2 horas al datetime y lo formatea como ISO.
    """
    # Sumar 2 horas al datetime
    dt_plus_2 = dt_input + timedelta(hours=2)
    
    # Devolver formateado
    return dt_plus_2.strftime("%Y-%m-%dT%H:%M:%S")

# --- 1) Leer inputs ---
# fb_obj: entrada de freeBusy de Google Calendar
# appointment_start_time: string "YYYY-MM-DDTHH:MM:SS"

appointment_dt_utc = parse_iso_maybe_z(appointment_start_time)

# --- 2) Parsear timeMin / timeMax ---
time_min_utc = parse_iso_maybe_z(fb_obj["timeMin"])
time_max_utc = parse_iso_maybe_z(fb_obj["timeMax"])

# --- 3) Extraer y convertir franjas 'busy' en UTC-aware ---
cal_entry = list(fb_obj["calendars"].values())[0]
busy_slots = []
for slot in cal_entry.get("busy", []):
    start = parse_iso_maybe_z(slot["start"])
    end   = parse_iso_maybe_z(slot["end"])
    busy_slots.append((start, end))

# --- 4) Determinar rango de fechas en hora local Madrid ---
start_local = time_min_utc.astimezone(MADRID).date()
end_local   = time_max_utc.astimezone(MADRID).date()

output = []
current_date = start_local

while current_date <= end_local:
    # Excluir domingos (weekday()==6 → domingo)
    if current_date.weekday() != 6:
        # Ventana diaria 08:00–19:00 en hora local Madrid
        window_start_local = datetime.combine(current_date, datetime.min.time(), tzinfo=MADRID) + timedelta(hours=6) # Esta dos horas por debajo lo que nos devuelve Google Calendar
        window_end_local   = datetime.combine(current_date, datetime.min.time(), tzinfo=MADRID) + timedelta(hours=17)
        # Convertir ventana a UTC para comparar slots ocupados
        window_start_utc = window_start_local.astimezone(UTC)
        window_end_utc   = window_end_local.astimezone(UTC)

        # Filtrar franjas busy que se solapen con ventana UTC
        busy_today = []
        for bs, be in busy_slots:
            if be > window_start_utc and bs < window_end_utc:
                busy_today.append((max(bs, window_start_utc), min(be, window_end_utc)))
        busy_today.sort(key=lambda x: x[0])

        # Calcular huecos libres en UTC y luego convertirlos a hora local
        frees = []
        cursor = window_start_utc
        for bs, be in busy_today:
            if bs > cursor:
                # Verificar que el intervalo libre tenga al menos 30 minutos
                duration = bs - cursor
                if duration >= timedelta(minutes=30):
                    frees.append({
                        "start": isoformat_madrid(cursor),
                        "end":   isoformat_madrid(bs)
                    })
            cursor = max(cursor, be)
        if cursor < window_end_utc:
            # Verificar que el último intervalo libre tenga al menos 30 minutos
            duration = window_end_utc - cursor
            if duration >= timedelta(minutes=30):
                frees.append({
                    "start": isoformat_madrid(cursor),
                    "end":   isoformat_madrid(window_end_utc)
                })

        output.append({
            "date": current_date.isoformat(),
            "free_slots": frees
        })
    current_date += timedelta(days=1)

# --- 5) Verificar cita existente en hora local Madrid ---
appointment_local = appointment_dt_utc.astimezone(MADRID)
interval_end_local = appointment_local + timedelta(minutes=30)

change_appointment = True
for day in output:
    if day["date"] == appointment_local.date().isoformat():
        for fs in day["free_slots"]:
            # Parsear y convertir ISO a aware Madrid
            fs_start = datetime.fromisoformat(fs["start"]).astimezone(MADRID)
            fs_end   = datetime.fromisoformat(fs["end"]).astimezone(MADRID)
            if fs_start <= appointment_local < fs_end and interval_end_local <= fs_end:
                change_appointment = False
                break
        break

# --- 6) Resultado global ---
result = {
    "data": output,
    "change_appointment": change_appointment
}

result